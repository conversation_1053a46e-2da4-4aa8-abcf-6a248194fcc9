---
import '../styles/global.css';
import Navbar from '../components/organisms/Navbar.astro';
import Footer from '../components/organisms/Footer.astro';

export interface Props {
	title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content="Astro description" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Arimo:ital,wght@0,400..700;1,400..700&family=Lato:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
	</head>
	<body>
		<Navbar />
		<slot />
		<Footer />
	</body>
</html>
