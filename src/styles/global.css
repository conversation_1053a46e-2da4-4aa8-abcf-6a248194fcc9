@import "tailwindcss";

@theme {
  /* Custom font families */
  --font-arimo: 'Arimo', sans-serif;
  --font-lato: 'Lato', sans-serif;

  /* Custom colors */
  --color-brand-dark-blue: #172039;
  --color-brand-dark-slate: #172C37;
  --color-brand-gold: #B29063;
  --color-brand-navy: #1F2A44;
  --color-brand-beige: #D7C4A9;
  --color-brand-light-gray: #E9E7E2;
}

/* Custom styles can be added below */
/* Example of custom component classes */
.btn-primary {
  background-color: var(--color-brand-dark-blue);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
}

.btn-secondary {
  background-color: var(--color-brand-gold);
  color: var(--color-brand-dark-blue);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
}

 .section-wrapper {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 24px;
  }

  @media (max-width: 768px) {
    .section-wrapper {
      padding: 0 16px;
    }
  }