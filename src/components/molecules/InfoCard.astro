---
export interface Props {
  iconSrc: string;
  title: string;
  description: string;
  iconAltText?: string;
}

const { iconSrc, title, description, iconAltText = 'Info card icon' } = Astro.props;

// Card Frame (e.g., 20:106 for Safety):
// - Layout: column, gap: 24px (gap-6)
// - Width: 294px (w-[294px])

// Text Frame (e.g., 22:181 for Safety text content):
// - Layout: column, gap: 12px (gap-3)

// Description Frame (e.g., 20:121 for Safety description):
// - Padding-right: 48px (pr-12)
---

<div class="flex flex-col gap-4 sm:gap-6 w-full md:w-[294px] text-left h-full">
  {/* Icon - Adjusted size for mobile */}
  <img src={iconSrc} alt={iconAltText} class="h-10 w-10 sm:h-12 sm:w-12 object-contain" />

  <div class="flex flex-col gap-2 sm:gap-3 flex-grow">
    <h3 class="font-arimo font-bold text-base sm:text-lg md:text-xl text-brand-dark-blue">
      {title}
    </h3>
    <div class="pr-0 md:pr-12"> {/* Padding-right from Figma node 20:121 */}
      <p class="font-arimo text-sm sm:text-base text-brand-dark-blue">
        {description}
      </p>
    </div>
  </div>
</div>
