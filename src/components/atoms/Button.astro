---
export interface Props {
  text: string;
  href?: string;
  variant?: 'primary' | 'secondary'; // Add more variants as needed
  showArrow?: boolean;
  extraClasses?: string;
}

const { text, href, variant = 'primary', showArrow = false, extraClasses = '' } = Astro.props;

// Base classes
let classes = "inline-flex items-center gap-3 px-3 py-3 border border-black font-arimo text-base leading-tight transition-colors duration-200"; // Matches Figma design (Frame 18:930)

// Variant specific classes
if (variant === 'primary') {
  classes += " text-brand-dark-blue hover:bg-brand-dark-blue hover:text-white"; // Based on Figma design with hover effect
} else if (variant === 'secondary') {
  classes += " text-white bg-brand-dark-blue hover:bg-brand-dark-blue/90"; // With hover effect
}

if (extraClasses) {
  classes += ` ${extraClasses}`;
}
---

{href ? (
  <a href={href} class={classes}>
    <span>{text}</span>
    {showArrow && (
      <span class="flex items-center ml-1">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-3 w-auto">
          <path d="M1 8H15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M8 1L15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    )}
  </a>
) : (
  <button type="button" class={classes}>
    <span>{text}</span>
    {showArrow && (
      <span class="flex items-center ml-1">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-3 w-auto">
          <path d="M1 8H15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M8 1L15 8L8 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    )}
  </button>
)}

<style>
  /* Arimo Hebrew Subset is specified in Figma.
     Tailwind uses a system font stack by default.
     We'd need to configure Tailwind to use Arimo or import it globally.
     For now, relying on Tailwind's default font and 'font-arimo' class if Arimo is set up in tailwind.config.
  */
  .font-arimo { /* Placeholder if Arimo is configured in Tailwind theme */
    font-family: 'Arimo Hebrew Subset', sans-serif; /* Fallback */
  }
</style>
