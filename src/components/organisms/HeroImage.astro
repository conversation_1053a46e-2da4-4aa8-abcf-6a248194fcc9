---
// Corresponds to Figma node 18:429 (Frame 812101)
// Layout: column, align items center, alignSelf stretch, gap 12px, padding 48px 96px, height 480px
// Fill: Image (hero_background.png)
---

<section
  class="h-[320px] sm:h-[400px] md:h-[480px] bg-cover bg-center bg-no-repeat px-6 md:px-24 py-12 flex flex-col items-center justify-center"
  style={`background-image: url('/assets/images/hero_background.png');`}
>
  {/*
    This frame in Figma (18:429) is defined with padding and alignment,
    but it doesn't have any children in the provided Figma data structure,
    only a background image fill.
    If there's content meant to be overlaid directly on this image within these paddings,
    it would be passed via a <slot />. For now, it's just the image display.
    The main hero text content appears in a separate frame (18:449) below this.
  */}
  <slot />
</section>
