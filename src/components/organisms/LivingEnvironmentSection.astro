---
import InfoCard from '../molecules/InfoCard.astro';

// Corresponds to Figma node 20:84 (Frame 812490)
// Background: #FFFFFF
// Layout: column, gap: 80px (gap-20), padding: 98px 96px (py-24 px-24)

const cardsData = [
  {
    iconSrc: "/assets/icons/icon_safety.svg",
    title: "Safety",
    description: "Ranked among the safest cities, providing a secure environment for families.",
    iconAltText: "Safety icon"
  },
  {
    iconSrc: "/assets/icons/icon_healthcare.svg",
    title: "Healthcare",
    description: "World-class medical facilities and easy access to services.",
    iconAltText: "Healthcare icon"
  },
  {
    iconSrc: "/assets/icons/icon_green_spaces.svg",
    title: "Green Spaces",
    description: "A city filled with parks and eco-friendly spaces.",
    iconAltText: "Green spaces icon"
  },
  {
    iconSrc: "/assets/icons/icon_cultural_diversity.svg",
    title: "Cultural Diversity",
    description: "Experience a vibrant mix of cultures, food, and festivals.",
    iconAltText: "Cultural diversity icon"
  }
];
---

<section class="bg-white w-full py-16 md:py-24 lg:py-[98px] px-6 md:px-24 lg:px-[96px]">
  <div class="max-w-[1248px] mx-auto flex flex-col items-center gap-16 md:gap-20"> {/* Centered content, gap from Figma */}

    {/* Title Block (Frame 20:97) */}
    <div class="w-full md:w-[612px] flex flex-col items-center justify-center gap-6 text-center">
      <div class="flex items-center gap-2">
        <span class="font-arimo font-bold text-lg text-brand-gold">-</span>
        <span class="font-arimo font-bold text-lg text-brand-gold mx-2">02</span>
        <span class="font-arimo font-bold text-lg text-brand-gold">-</span>
      </div>
      <h2 class="font-arimo font-bold text-3xl md:text-4xl text-brand-dark-blue">
        Singaporean Living Environment
      </h2>
    </div>

    {/* Cards Container (Frame 20:112) */}
    {/* Layout: row, gap: 24px. Using grid for responsiveness with 2 columns on mobile */}
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 w-full">
      {cardsData.map(card => (
        <InfoCard
          iconSrc={card.iconSrc}
          title={card.title}
          description={card.description}
          iconAltText={card.iconAltText}
        />
      ))}
    </div>
  </div>
</section>
