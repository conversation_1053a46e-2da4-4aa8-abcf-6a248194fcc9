---
import <PERSON><PERSON> from '../atoms/Button.astro';

// Corresponds to Figma node 18:449 (Frame 812103) and its children.
// Parent Frame 18:449:
// - Background: #FFFFFF (fill_00Q1GX)
// - Layout: row, justify-center, gap 24px
// - Padding: 98px 96px (approx py-24 px-24)

// Content Frame 18:979 (child of 18:449):
// - Layout: column, align-items: flex-end, gap 56px (gap-14)
// - Width: 1036px

// Inner Text Block Frame 18:904 (child of 18:979):
// - Layout: column, justify-content: center, gap 48px (gap-12)
// - Width: 825px
---

<section class="bg-white w-full py-16 md:py-24 lg:py-[98px] px-6 md:px-24 lg:px-[96px]"> {/* Exact padding from Figma: 98px 96px */}
  <div class="relative max-w-[1036px] mx-auto flex flex-col items-end gap-10 md:gap-14 pl-0 md:pl-[80px]"> {/* Container from 18:979 with padding only on larger screens */}

    {/* Decorative Rectangle 19:183 - gold accent element */}
    <div
      class="absolute left-0 top-0 w-[8px] h-[300px] bg-brand-gold hidden lg:block"
      style="left: 40px;"
      aria-hidden="true"
    ></div>

    <div class="w-full md:w-[825px] flex flex-col justify-center items-start gap-8 md:gap-12 text-left"> {/* Text block from 18:904, items-start for text-left */}
      <h1 class="font-arimo font-bold text-3xl sm:text-4xl md:text-5xl lg:text-[48px] uppercase text-brand-dark-blue leading-tight">
        Singapore: Your Gateway to Global Success
      </h1>
      <p class="font-arimo text-lg sm:text-xl md:text-2xl lg:text-[24px] text-brand-dark-blue leading-snug">
        Discover why Singapore is the perfect place to start or expand your business. With its strategic location, strong economy, and vibrant lifestyle, Singapore offers everything you need to succeed.
      </p>
      <Button
        text="Start your Singapore Journey now"
        href="/contact"
        variant="primary"
        showArrow={true}
        extraClasses="mt-4 hidden"
      />
    </div>

  </div>
</section>
