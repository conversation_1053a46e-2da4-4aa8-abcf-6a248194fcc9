---
// src/components/organisms/WhatWeDoSection.astro
const title = "What we do";
const paragraph = "We provide tailored solutions across company incorporation, legal structuring, operational deployment, and talent acquisition—ensuring our clients launch with clarity and confidence. Acting as a vital bridge between clients and Singapore’s regulatory ecosystem, we streamline interactions with government agencies and licensing bodies to ensure a smooth, compliant entry process.";
const imageUrl = "/assets/images/what_we_do_section_image.png"; // Updated image
const imageAltText = "Abstract representation of services and solutions."; // Generic alt text, can be improved
---

<section class="flex flex-col md:flex-row items-stretch bg-white mt-[80px] w-full">
  {/* Image on the left for md and up, full width on small screens */}
  <div class="w-full md:w-1/2">
    <img src={imageUrl} alt={imageAltText} class="w-full h-full object-cover min-h-[300px] md:min-h-0" />
  </div>

  {/* Text content on the right for md and up, below image on small screens */}
  <div class="w-full md:w-1/2 flex items-center justify-center">
    <div class="p-8 md:p-16 lg:p-24 xl:p-[96px_96px_96px_130px] max-w-2xl mx-auto md:mx-0"> {/* Adjusted padding based on Figma */}
      <h2 class="text-lg font-bold text-brand-gold mb-4 md:mb-8 text-right" style="font-family: 'Arimo Hebrew Subset', sans-serif; font-size: 18px; color: #B29063;">
        {title}
      </h2>
      <p class="text-base text-brand-dark-blue font-arimo leading-normal text-left" style="font-family: 'Arimo Hebrew Subset', sans-serif; font-size: 16px; color: #1F2A44;">
        {paragraph}
      </p>
    </div>
  </div>
</section>

<style>
/* Ensure Arimo fonts are available. You might need to import them in your global.css or Layout.astro */
@import url('https://fonts.googleapis.com/css2?family=Arimo:ital,wght@0,400..700;1,400..700&family=Arimo+Hebrew+Subset:wght@400;700&display=swap');

/* Fallback fonts if Arimo is not loaded */
h2, p {
  font-family: 'Arimo Hebrew Subset', 'Arial', sans-serif; /* Consolidating for simplicity, specific styles applied inline or via Tailwind */
}
</style>
