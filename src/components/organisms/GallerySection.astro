---
// src/components/organisms/GallerySection.astro
---
<section class="w-full bg-[#E9E7E2]">
  <div class="container mx-auto max-w-full xl:max-w-screen-xl"> {/* Overall content wrapper with horizontal padding */}
    <div class="flex flex-col lg:flex-row w-full justify-between items-stretch"> {/* Negative margin to allow image to bleed to edge of its column on small screens if needed, or remove if image is always contained */}
      
      {/* Image Section */}
      {/* On large screens, image takes up to 824px. On smaller, it's full width of its column. */}
      {/* The background #E9E7E2 from the parent section covers this area. */}
      <div class="w-full lg:w-auto lg:flex-1 lg:max-w-[824px] h-[300px] md:h-[462px] mb-6 lg:mb-0 px-6 lg:px-0"> {/* Added px-6 lg:px-0 to contain image within column padding on small screens */}
        <img 
          src="/assets/images/gallery/gallery_main_image.png" 
          alt="Gallery Showcase" 
          class="w-full h-full object-cover"
        />
      </div>

      {/* Text Content Section */}
      {/* On large screens, text content takes up to 424px with specific left padding. */}
      {/* On smaller screens, it takes full width of its column. */}
      <div class="w-full lg:w-auto lg:flex-none lg:max-w-[424px] flex flex-col justify-center text-center lg:text-left relative px-6">
        <h2 class="text-[#B29063] font-arimo font-bold text-lg mb-6">
          Gallery
        </h2>
        <p class="text-[#172039] font-arimo text-base mb-4">
          Explore a visual showcase of our journey, partnerships, and regional footprint.
        </p>
        <p class="text-[#172039] font-arimo text-base mb-8">
          From client engagements and government collaborations to infrastructure deployments and innovation highlights, our gallery reflects the depth and diversity of FusX Global’s impact across Southeast Asia.
        </p>
        <div class="flex justify-end absolute bottom-0 right-0">
          <button class="bg-[#1F2A44] text-white px-8 py-3 flex items-center justify-center">
            <img src="/assets/images/gallery/chevron_right_white.svg"  alt="Previous" class="w-2 h-4" />
          </button>
          <button class="bg-white text-[#1F2A44] px-8 py-3 flex items-center justify-center transition-colors">
            <img src="/assets/images/gallery/chevron_right_dark.svg" alt="Next" class="w-2 h-4" />
          </button>
        </div>
      </div>

    </div>
  </div>
</section>

<style>
  /* Ensure Arimo font is loaded if not globally available via Tailwind config */
  @import url('https://fonts.googleapis.com/css2?family=Arimo:wght@400;700&display=swap');
  .font-arimo {
    font-family: 'Arimo', sans-serif;
  }
</style>
