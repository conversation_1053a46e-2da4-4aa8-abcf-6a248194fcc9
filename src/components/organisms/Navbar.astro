---
import Logo from '../molecules/Logo.astro';
import Button from '../atoms/Button.astro';

// State for mobile menu toggle
let isMobileMenuOpen = false; // This will be managed by client-side script
---

<nav class="bg-white w-full sticky top-0 z-50 shadow-sm">
  <div class="container mx-auto px-6 md:px-24 lg:px-24 py-5 flex justify-between items-center max-w-[1440px]">
    <Logo href="/" extraClasses="py-1" />

    {/* Desktop Navigation Links */}
    <div class="hidden md:flex items-center space-x-8">
      <a href="/" class="text-lg font-arimo text-brand-dark-slate hover:text-brand-dark-blue">Home</a>
      <a href="/about" class="text-lg font-arimo text-brand-dark-slate hover:text-brand-dark-blue">About</a>
      <Button
        text="Start your Singapore Journey now"
        href="/contact"
        variant="primary"
        extraClasses="border-none text-lg md:text-xl font-arimo text-brand-dark-slate hover:text-brand-dark-blue"
      />
    </div>

    {/* Mobile Menu Button (Hamburger) */}
    <div class="md:hidden">
      <button id="mobile-menu-button" aria-label="Open mobile menu" aria-expanded="false" class="text-brand-dark-slate focus:outline-none">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
      </button>
    </div>
  </div>

  {/* Mobile Menu - Collapsible */}
  <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg absolute w-full">
    <div class="px-6 pt-2 pb-4 space-y-3">
      <a href="/" class="block py-2 text-lg font-arimo text-brand-dark-slate hover:text-brand-dark-blue">Home</a>
      <a href="/about" class="block py-2 text-lg font-arimo text-brand-dark-slate hover:text-brand-dark-blue">About</a>
      <Button
        text="Start your Singapore Journey now"
        href="/contact"
        variant="primary"
        extraClasses="border-none text-lg font-arimo text-brand-dark-slate w-full text-left hover:text-brand-dark-blue"
      />
    </div>
  </div>
</nav>

<script>
  const menuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  let isMenuOpen = false;

  menuButton?.addEventListener('click', () => {
    isMenuOpen = !isMenuOpen;
    mobileMenu?.classList.toggle('hidden', !isMenuOpen);
    menuButton.setAttribute('aria-expanded', isMenuOpen.toString());

    // Optional: Change hamburger to X icon when open
    const svgIcon = menuButton.querySelector('svg path');
    if (svgIcon) {
      if (isMenuOpen) {
        svgIcon.setAttribute('d', 'M6 18L18 6M6 6l12 12'); // X icon
      } else {
        svgIcon.setAttribute('d', 'M4 6h16M4 12h16m-7 6h7'); // Hamburger icon
      }
    }
  });
</script>
