---
// src/components/organisms/OurClient.astro

// Define client data - this could also come from props or a data file
const clients = [
  {
    id: 'sinomach',
    logo: '/assets/images/our-clients/sinomach_logo.png',
    logoWidth: 294, // from Figma: 438:154
    logoHeight: 49, // from Figma: 438:154
    name: 'Sinomach',
    description: "Sinomach, short for China National Machinery Industry Corporation, is a state-owned enterprise under the direct supervision of the Chinese central government. Established in 1997, it is one of China's largest engineering and manufacturing conglomerates. Sinomach primarily engages in equipment manufacturing, project contracting, trade and services, and R&D.",
    image: '/assets/images/our-clients/sinomach_section_image.png',
    altLogo: 'Sinomach Logo',
    altImage: 'Sinomach Products Showcase'
  },
  {
    id: 'chengdu_farwest',
    logo: '/assets/images/our-clients/chengdu_farwest_logo.png',
    logoWidth: 212, // from Figma: 438:164
    logoHeight: 78, // from Figma: 438:164
    name: 'Chengdu Farwest Energy Technology',
    description: "Chengdu Farwest Energy Technology Co., Ltd. specializes in R&D, international trade, oilfield technology services and business consulting. The company's headquarters, R&D center and trade office, are located in Chengdu, Sichuan Province, with a production base, warehouse and support center in Nanshan Science and Technology Park, Deyang City.",
    image: '/assets/images/our-clients/chengdu_farwest_section_image.png',
    altLogo: 'Chengdu Farwest Energy Technology Logo',
    altImage: 'Chengdu Farwest Energy Technology Showcase'
  },
  {
    id: 'eaglestar',
    logo: '/assets/images/our-clients/eaglestar_logo.png',
    logoWidth: 212, // from Figma: 438:176 (assuming same as Chengdu based on layout_K7E21B)
    logoHeight: 78, // from Figma: 438:176
    name: 'Eaglestar Energy Technology',
    description: "Eaglestar Energy Technology Co., Ltd. is a leading Chinese manufacturer specializing in petroleum and energy equipment. Founded in 2005 and headquartered in Zhengzhou, Henan Province, the company has established a strong presence in the global market, particularly in Africa, Southeast Asia, and the Middle East.",
    image: '/assets/images/our-clients/eaglestar_section_image.png',
    altLogo: 'Eaglestar Energy Technology Logo',
    altImage: 'Eaglestar Energy Technology Showcase'
  }
];

// All client sections will be displayed vertically.
---

<section id="our-clients" class="bg-gray-100 py-24 px-6 md:px-12 lg:px-24 flex flex-col items-center gap-14">
  <div class="w-full max-w-6xl flex flex-col gap-6 items-start lg:items-stretch lg:pr-[424px]">
    <h3 class="text-brand-gold font-arimo-hebrew font-bold text-lg text-left lg:text-right w-full">Our clients</h3>
    <h2 class="text-brand-navy font-arimo-hebrew font-bold text-3xl md:text-4xl leading-tight text-left">
      We serve a diverse portfolio of clients spanning government bodies, NGOs, and high-impact enterprises across critical sectors.
    </h2>
    <p class="text-brand-dark-blue font-arimo text-base leading-relaxed text-left">
      From public agencies and multilateral institutions to oil & gas operators, AI innovators, and logistics leaders—our clients trust us to navigate complex markets with precision, discretion, and strategic foresight.
    </p>
  </div>

  <!-- Client Sections Container -->
  <div class="w-full max-w-6xl flex flex-col gap-14 md:gap-20"> {/* Added gap for spacing between client sections */}
    {clients.map((client, index) => (
      <div id={`client-${client.id}`} class="client-section w-full">
        <div class={`flex flex-col lg:flex-row items-center gap-8 lg:gap-16 xl:gap-32 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
          <!-- Text Content -->
          <div class="w-full lg:w-[400px] flex flex-col gap-10 text-left">
            <img 
              src={client.logo} 
              alt={client.altLogo} 
              width={client.logoWidth}
              height={client.logoHeight}
              class="object-contain self-start"
              loading="lazy"
            />
            <p class="text-brand-dark-blue font-arimo text-base leading-relaxed">
              {client.description}
            </p>
          </div>

          <!-- Image Content -->
          <div class="w-full lg:flex-1 h-[300px] md:h-[400px] lg:h-[462px] bg-brand-light-gray relative overflow-hidden group">
            <img 
              src={client.image} 
              alt={client.altImage} 
              class="w-full h-full object-cover"
              loading="lazy"
            />
            <!-- Navigation Arrows for this slide -->
            <div class="absolute bottom-0 right-0 flex items-center justify-end">
              <button 
                aria-label="Previous client"
                class="prev-client-btn bg-brand-navy  px-8 py-3 shadow-md transition-colors"
                title="Navigation (currently not interactive for main carousel)"
              >
                <img src="/assets/images/our-clients/chevron_right_white.svg" alt="Previous" class="w-2 h-4 transform rotate-180 scale-x-[-1]"/>
              </button>
              <button 
                aria-label="Next client"
                class="next-client-btn bg-white hover:bg-gray-100  px-8 py-3 shadow-md transition-colors"
                title="Navigation (currently not interactive for main carousel)"
              >
                {/* This file is misnamed, it's actually a right-pointing dark arrow */}
                <img src="/assets/images/our-clients/chevron_left_dark.svg" alt="Next" class="w-2 h-4"/>
              </button>
            </div>
          </div>
        </div>
      </div>
    ))}
  </div>
</section>

<!-- 
  Removed client-side script for slider functionality. 
  All clients are now displayed vertically.
  The navigation arrows on each client's image are part of the Figma design;
  they are not hooked up to change the main displayed client in this version.
  They could be used for an image gallery within each client section if needed in the future.
-->

<style>
  /* Custom font families if not globally defined via Tailwind */
  /* Ensure these fonts are imported in your global.css or layout */
  .font-arimo-hebrew {
    font-family: 'Arimo Hebrew Subset', sans-serif; /* Fallback to sans-serif */
  }
  .font-arimo {
    font-family: 'Arimo', sans-serif; /* Fallback to sans-serif */
  }

  /* Tailwind custom colors are defined in tailwind.config.mjs.
     This component uses:
     - bg-gray-100 (standard Tailwind)
     - text-brand-gold
     - text-brand-navy
     - text-brand-dark-blue
     - bg-brand-light-gray
     Ensure these (except gray-100) are present in your Tailwind config.
     The Arimo fonts should also be configured in tailwind.config.mjs and imported globally.
  */
</style>
