---
// Corresponds to Figma node 32:559 (Frame 812498)
// Background: #1F2A44 (brand-navy)
// Text color 1: #FFFFFF (white)
// Text color 2: #B29063 (brand-gold)
// Layout: column, align-items: flex-end, gap: 80px, padding: 48px 96px

const footerLinks = [
  { id: "benefits", number: "01", text: "Benefits of Setting Up Companies in Singapore" },
  { id: "living", number: "02", text: "Singaporean Living Environment" },
  { id: "investment", number: "03", text: "Singaporean Investment Climate and Opportunities" },
  { id: "education", number: "04", text: "Education in Singapore" },
  { id: "entrepreneurship", number: "05", text: "Singaporean Entrepreneurship Environment" },
  { id: "family-offices", number: "06", text: "Setting Up Family Offices in Singapore" }
];
---

<footer class="bg-brand-navy py-12 md:py-16 px-6 md:px-24">
  <div class="max-w-[1248px] mx-auto flex flex-col items-end gap-12 md:gap-20">

    {/* Top Text Block */}
    <div class="w-full flex flex-col md:flex-row justify-between items-start md:items-center">
      <h2 class="font-arimo font-bold text-3xl text-white">
        Singapore
      </h2>
      <p class="font-arimo text-xl text-brand-gold mt-4 md:mt-0">
        You're ready to join Singapore?
      </p>
    </div>

    {/* Links Grid - 3x2 layout as shown in Figma */}
    <div class="w-fit flex flex-col  gap-6">
      {/* First row */}
      <div class="flex flex-col lg:flex-row gap-6 justify-end">
        {footerLinks.slice(0, 4).map(link => (
          <a href={`#${link.id}`} class="w-full lg:w-[188px] flex flex-col gap-3 hover:opacity-80 transition-opacity">
            <span class="font-arimo font-bold text-base text-brand-gold">
              {link.number}
            </span>
            <span class="font-arimo text-sm text-white">
              {link.text}
            </span>
          </a>
        ))}
      </div>

      {/* Second row */}
      <div class="flex flex-col lg:flex-row gap-6">
        {footerLinks.slice(4, 6).map(link => (
          <a href={`#${link.id}`} class="w-full lg:w-[188px] flex flex-col gap-3 hover:opacity-80 transition-opacity">
            <span class="font-arimo font-bold text-base text-brand-gold">
              {link.number}
            </span>
            <span class="font-arimo text-sm text-white">
              {link.text}
            </span>
          </a>
        ))}
      </div>
    </div>
  </div>
</footer>
