---
// Corresponds to Figma node 27:308 (Frame 812495)
// BG Image: section_05_bg.png
// Overlay: rgba(31, 42, 68, 0.95) -> bg-[#1F2A44]/95
// Layout: row, gap 24px, padding 98px 96px, height 720px

const features = [
  {
    title: "Government Support:",
    text: "Grants and initiatives like Startup SG help new businesses."
  },
  {
    title: "Easy Regulations:",
    text: "Business laws are straightforward, with minimal red tape."
  },
  {
    title: "Access to Funding:",
    text: "Strong venture capital and investment networks."
  },
  {
    title: "Active Community:",
    text: "Thriving startup scene with plenty of networking opportunities."
  }
];
---

<section 
  class="relative py-16 md:py-24 px-6 md:px-24 bg-cover bg-center min-h-[720px] flex items-center"
  style={`background-image: url('/assets/images/section_05_bg.png');`}
>
  {/* Overlay */}
  <div class="absolute inset-0 bg-brand-navy opacity-95 z-0"></div>

  <div class="relative z-10 max-w-[1248px] mx-auto w-full flex flex-col lg:flex-row gap-6 md:gap-12 items-stretch">
    
    {/* Left Column: Text Content (Frame 28:352) */}
    <div class="w-full lg:w-1/2 flex flex-col justify-between gap-10 py-8">
      {/* Title Block */}
      <div class="text-left">
        <p class="font-arimo font-bold text-lg text-brand-gold">
          - 05 -
        </p>
        <h2 class="font-arimo font-bold text-3xl md:text-4xl text-white mt-2">
          Singaporean Entrepreneurship Environment
        </h2>
      </div>

      {/* Features Grid (Frame 28:351) */}
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-8">
        {features.map(feature => (
          <div class="flex flex-col gap-3">
            <h3 class="font-lato font-bold text-xl text-brand-gold">
              {feature.title}
            </h3>
            <p class="font-lato text-base text-white">
              {feature.text}
            </p>
          </div>
        ))}
      </div>
    </div>

    {/* Right Column: Image (Frame 27:316) */}
    <div class="w-full lg:w-1/2 flex items-center justify-center lg:min-h-[calc(720px-192px)]"> {/* 192px is approx py-24 * 2 */}
      <img 
        src="/assets/images/section_05_col_img.png" 
        alt="Singapore Entrepreneurship Environment" 
        class="w-full h-auto max-h-[500px] lg:max-h-full object-contain rounded-lg"
      />
    </div>
  </div>
</section>
