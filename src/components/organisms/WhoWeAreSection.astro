---
// src/components/organisms/WhoWeAreSection.astro
const title = "Who we are";
const paragraph = "FusX Global is a premier management and consultancy firm dedicated to facilitating seamless market entry and sustainable expansion for overseas companies looking to establish a strong presence in Singapore. With deep expertise in localisation, regulatory compliance, and operational strategy, FusX Global serves as a trusted partner in navigating the complexities of setting up and scaling within one of Asia’s most dynamic business hubs.";
const backgroundImage = "/assets/images/who_we_are_section_bg.png";
const backgroundAltText = "A group of professionals in a meeting, discussing work.";
---

<section class="relative flex flex-col items-end justify-end min-h-[600px] md:min-h-[700px] lg:min-h-[800px] py-8">
  <img src={backgroundImage}  alt={backgroundAltText} class="transform scale-x-[-1] absolute inset-0 w-full h-[540px] object-top object-cover" />

    <div class="section-wrapper">

  <div class="flex justify-end relative w-full"> {/* Removed container mx-auto px-4 since it's now in the wrapper */}
    <div class="bg-white p-8 md:p-12 shadow-xl max-w-[920px] w-full">
      <h2 class="text-2xl md:text-3xl font-bold text-left mb-6 md:mb-8 text-brand-gold" style="font-family: 'Arimo Hebrew Subset', sans-serif;">
        {title}
      </h2>
      <p class="text-base md:text-lg text-left text-brand-dark-blue font-arimo leading-normal">
        {paragraph}
      </p>
    </div>
  </div>
    </div>
</section>

<style>
/* Ensure Arimo fonts are available. You might need to import them in your global.css or Layout.astro */
@import url('https://fonts.googleapis.com/css2?family=Arimo:ital,wght@0,400..700;1,400..700&family=Arimo+Hebrew+Subset:wght@400;700&display=swap');

/* Fallback fonts if Arimo is not loaded */
h2 {
  font-family: 'Arimo Hebrew Subset', 'Arial', sans-serif;
}
p {
  font-family: 'Arimo', 'Helvetica', sans-serif;
}
</style>
