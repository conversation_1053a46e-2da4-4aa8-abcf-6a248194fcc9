/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      fontFamily: {
        arimo: ['Arimo', 'sans-serif'],
        'arimo-hebrew': ['Arimo Hebrew Subset', 'sans-serif'],
        lato: ['Lato', 'sans-serif'],
      },
      colors: {
        'brand-dark-blue': '#172039',
        'brand-dark-slate': '#172C37',
        'brand-gold': '#B29063',
        'brand-navy': '#1F2A44',
        'brand-beige': '#D7C4A9', // Solid version of the overlay color
        'brand-light-gray': '#E9E7E2',
        // white and black are available by default
      },
    },
  },
  plugins: [],
};
